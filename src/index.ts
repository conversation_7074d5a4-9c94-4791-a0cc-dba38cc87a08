import { parseArgs } from 'node:util';
import { JiraClient } from './jira.js';
import { loadGitRepos } from './git.js';
import repositories from './repositories.json';
import { setupClaudeCodeSettings } from './claude/setup-claude-code-settings.js';
import { runClaude } from './claude/run-claude.js';
import { buildAllowedToolsString, buildDisallowedToolsString, generatePrompt } from './create-prompt.js';
import { tmpdir } from 'node:os';
import { mkdir, writeFile } from "node:fs/promises";

const claudeCodeOAuthToken = process.env.CLAUDE_CODE_OAUTH_TOKEN;
const allowedTools = process.env.INPUT_ALLOWED_TOOLS?.split(",");
const disallowedTools = process.env.INPUT_DISALLOWED_TOOLS?.split(",");
const reposDir = process.env.REPOS_DIR || './repos';

// Wait for <PERSON> to finish with timeout
let timeoutMs = 10 * 60 * 1000; // Default 10 minutes
if (process.env.INPUT_TIMEOUT_MINUTES) {
  const envTimeout = parseInt(process.env.INPUT_TIMEOUT_MINUTES, 10);
  if (isNaN(envTimeout) || envTimeout <= 0) {
    throw new Error(`INPUT_TIMEOUT_MINUTES must be a positive number, got: ${process.env.INPUT_TIMEOUT_MINUTES}`);
  }
  timeoutMs = envTimeout * 60 * 1000;
}

async function run(): Promise<void> {
  const { values: { 'issue-id': issueId } } = parseArgs({
    args: process.argv.slice(2),
    options: {
      'issue-id': {
        type: 'string',
        short: 'i',
      }
    },
  });
  if (!issueId) {
    throw new Error('JIRA issue ID is required');
  }

  const jiraClient = new JiraClient({
    serverUrl: process.env.JIRA_SERVER_URL,
    credentials: process.env.JIRA_CREDENTIALS,
  });

  console.log(`Processing JIRA issue: ${issueId}`);

  // Step 1: Check if issue belongs to a known project and load git repos
  const key = issueId.match(/^([A-Z]+)-[0-9]+$/)?.[1];
  if (key) {
    const repos = Object.entries(repositories)
      .filter(([, projects]) => projects.includes(key))
      .map(([repo]) => repo);
    if (repos.length) {
      await loadGitRepos(repos, reposDir);
    } else {
      throw new Error(`Project '${key}' not found in repositories.json`);
    }
  } else {
    throw new Error(`Could not extract project key from issue ID: ${issueId}`);
  }

  // Step 2: Create initial tracking comment
  const issue = jiraClient.issue(issueId);
  await issue.comments().post('is working…');

  await setupClaudeCodeSettings(process.env.INPUT_SETTINGS);

  // Generate the prompt
  const promptContent = generatePrompt(preparedContext, githubData);

  // Log the final prompt to console
  console.log("===== FINAL PROMPT =====");
  console.log(promptContent);
  console.log("=======================");

  // Write the prompt file
  const tempPath = tmpdir();
  const promptPath = `${tempPath}/claude-prompts/claude-prompt.txt`;
  const dirPath = promptPath.substring(0, promptPath.lastIndexOf("/"));
  await mkdir(dirPath, { recursive: true });
  await writeFile(promptPath, promptContent);

  const mcpServers: Record<string, unknown> = {
    "jira-comment": {
      command: "node",
      args: [
        "run",
        `/src/mcp/github-comment-server.ts`,
      ],
      env: {
        JIRA_SERVER_URL: process.env.JIRA_SERVER_URL,
        JIRA_CREDENTIALS: process.env.JIRA_CREDENTIALS,
        JIRA_ISSUE_ID: process.env.JIRA_ISSUE_ID,
      },
    },
  }

  if (!claudeCodeOAuthToken) {
    throw new Error("CLAUDE_CODE_OAUTH_TOKEN is required.");
  }

  await runClaude(tempPath, promptPath, {
    allowedTools: buildAllowedToolsString(allowedTools),
    disallowedTools: buildDisallowedToolsString(disallowedTools, allowedTools),
    mcpConfig: JSON.stringify(mcpServers, null, 2),
  });
}

run().catch((error) => {
  console.error(error.message);
  process.exit(1);
});
