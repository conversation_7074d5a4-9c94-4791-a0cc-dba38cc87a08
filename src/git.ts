import { exec } from 'node:child_process';
import { promisify } from 'node:util';
import { existsSync } from 'node:fs';
import { mkdir } from 'node:fs/promises';
import { basename, join } from 'node:path';

const execAsync = promisify(exec);

export async function loadGitRepos(repos: string[], reposDir: string): Promise<void> {
  console.log(`Found ${repos.length} repositories for project:`);

  // Ensure repos directory exists
  if (!existsSync(reposDir)) {
    await mkdir(reposDir, { recursive: true });
    console.log(`Created repositories directory: ${reposDir}`);
  }

  for (const repo of repos) {
    console.log(`Processing repository: ${repo}`);

    // Extract repository name from URL
    const repoName = basename(repo, '.git');
    const localPath = join(reposDir, repoName);

    try {
      if (existsSync(localPath)) {
        console.log(`  Repository ${repoName} already exists, pulling latest changes...`);
        await execAsync('git pull', { cwd: localPath });
        console.log(`  ✓ Updated ${repoName}`);
      } else {
        console.log(`  Cloning ${repoName}...`);
        await execAsync(`git clone ${repo} --depth 1 ${localPath}`);
        console.log(`  ✓ Cloned ${repoName}`);
      }
    } catch (error) {
      console.error(`  ✗ Failed to process ${repoName}:`, error instanceof Error ? error.message : error);
    }
  }
}
