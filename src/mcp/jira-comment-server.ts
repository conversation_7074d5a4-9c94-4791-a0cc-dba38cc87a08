#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { JiraClient } from "../jira";

const serverUrl = process.env.JIRA_SERVER_URL;
const credentials = process.env.JIRA_CREDENTIALS;
const jiraIssueId = process.env.JIRA_ISSUE_ID;

// JIRA Comment MCP Server - Minimal server that only provides comment update functionality
const server = new McpServer({
  name: "JIRA Comment Server",
  version: "0.0.1",
});

server.tool(
  "update_claude_comment",
  "Update the Claude comment with progress and results",
  {
    body: z.string().describe("The updated comment content"),
  },
  async ({ body }) => {
    try {
      if (!jiraIssueId) {
        throw new Error("JIRA_ISSUE_ID environment variable is required");
      }

      const jiraClient = new JiraClient({ serverUrl, credentials });
      const result = await jiraClient.comments(jiraIssueId).post(body);

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        content: [
          {
            type: "text",
            text: `Error: ${errorMessage}`,
          },
        ],
        error: errorMessage,
        isError: true,
      };
    }
  },
);

async function runServer() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  process.on("exit", () => {
    server.close();
  });
}

runServer().catch(console.error);
