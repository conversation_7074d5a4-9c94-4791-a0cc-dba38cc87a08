pipeline {
  agent any

  environment {
    NODE = "jod"
  }

  stages {
    stage('NPM install') {
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'touch .env'
            sh 'touch package-lock.json'
            sh 'rm package-lock.json'
            sh 'rm -rf node_modules'
            sh 'npm i'
          }
        }
      }
    }
  }
}
